/*! tailwindcss v4.1.10 | MIT License | https://tailwindcss.com */
@layer properties{@supports (((-webkit-hyphens:none)) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-space-x-reverse:0;--tw-font-weight:initial;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-border-style:solid}}}@layer theme{:root,:host{--font-sans:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--font-mono:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--color-red-100:oklch(93.6% .032 17.717);--color-red-500:oklch(63.7% .237 25.331);--color-yellow-100:oklch(97.3% .071 103.193);--color-yellow-500:oklch(79.5% .184 86.047);--color-emerald-100:oklch(95% .052 163.051);--color-emerald-500:oklch(69.6% .17 162.48);--color-blue-100:oklch(93.2% .032 255.585);--color-blue-500:oklch(62.3% .214 259.815);--color-gray-100:oklch(96.7% .003 264.542);--color-gray-200:oklch(92.8% .006 264.531);--color-gray-400:oklch(70.7% .022 261.325);--color-gray-500:oklch(55.1% .027 264.364);--color-gray-600:oklch(44.6% .03 256.802);--color-gray-700:oklch(37.3% .034 259.733);--color-gray-800:oklch(27.8% .033 256.848);--color-gray-900:oklch(21% .034 264.665);--color-black:#000;--color-white:#fff;--spacing:.25rem;--text-sm:.875rem;--text-sm--line-height:calc(1.25/.875);--text-xl:1.25rem;--text-xl--line-height:calc(1.75/1.25);--font-weight-medium:500;--font-weight-bold:700;--radius-xl:.75rem;--default-transition-duration:.15s;--default-transition-timing-function:cubic-bezier(.4,0,.2,1);--default-font-family:var(--font-sans);--default-mono-font-family:var(--font-mono)}}@layer base{*,:after,:before,::backdrop{box-sizing:border-box;border:0 solid;margin:0;padding:0}::file-selector-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}html,:host{-webkit-text-size-adjust:100%;tab-size:4;line-height:1.5;font-family:var(--default-font-family,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji");font-feature-settings:var(--default-font-feature-settings,normal);font-variation-settings:var(--default-font-variation-settings,normal);-webkit-tap-highlight-color:transparent}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:var(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace);font-feature-settings:var(--default-mono-font-feature-settings,normal);font-variation-settings:var(--default-mono-font-variation-settings,normal);font-size:1em}small{font-size:80%}sub,sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}:-moz-focusring{outline:auto}progress{vertical-align:baseline}summary{display:list-item}ol,ul,menu{list-style:none}img,svg,video,canvas,audio,iframe,embed,object{vertical-align:middle;display:block}img,video{max-width:100%;height:auto}button,input,select,optgroup,textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}:where(select:is([multiple],[size])) optgroup{font-weight:bolder}:where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}::file-selector-button{margin-inline-end:4px}::placeholder{opacity:1}@supports (not ((-webkit-appearance:-apple-pay-button))) or (contain-intrinsic-size:1px){::placeholder{color:currentColor}@supports (color:color-mix(in lab, red, red)){::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}}textarea{resize:vertical}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}::-webkit-datetime-edit{display:inline-flex}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-datetime-edit{padding-block:0}::-webkit-datetime-edit-year-field{padding-block:0}::-webkit-datetime-edit-month-field{padding-block:0}::-webkit-datetime-edit-day-field{padding-block:0}::-webkit-datetime-edit-hour-field{padding-block:0}::-webkit-datetime-edit-minute-field{padding-block:0}::-webkit-datetime-edit-second-field{padding-block:0}::-webkit-datetime-edit-millisecond-field{padding-block:0}::-webkit-datetime-edit-meridiem-field{padding-block:0}:-moz-ui-invalid{box-shadow:none}button,input:where([type=button],[type=reset],[type=submit]){appearance:button}::file-selector-button{appearance:button}::-webkit-inner-spin-button{height:auto}::-webkit-outer-spin-button{height:auto}[hidden]:where(:not([hidden=until-found])){display:none!important}}@layer components;@layer utilities{.filament-spatie-health .static{position:static}.filament-spatie-health .-mt-1{margin-top:calc(var(--spacing)*-1)}.filament-spatie-health .mt-0{margin-top:calc(var(--spacing)*0)}.filament-spatie-health .mb-5{margin-bottom:calc(var(--spacing)*5)}.filament-spatie-health .contents{display:contents}.filament-spatie-health .flex{display:flex}.filament-spatie-health .grid{display:grid}.filament-spatie-health .h-6{height:calc(var(--spacing)*6)}.filament-spatie-health .w-6{width:calc(var(--spacing)*6)}.filament-spatie-health .transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.filament-spatie-health .grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.filament-spatie-health .items-center{align-items:center}.filament-spatie-health .items-start{align-items:flex-start}.filament-spatie-health .justify-center{justify-content:center}.filament-spatie-health .gap-6{gap:calc(var(--spacing)*6)}:where(.filament-spatie-health .space-x-2>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))}.filament-spatie-health .overflow-hidden{overflow:hidden}.filament-spatie-health .rounded-full{border-radius:3.40282e38px}.filament-spatie-health .rounded-xl{border-radius:var(--radius-xl)}.filament-spatie-health .bg-blue-100{background-color:var(--color-blue-100)}.filament-spatie-health .bg-emerald-100{background-color:var(--color-emerald-100)}.filament-spatie-health .bg-gray-100{background-color:var(--color-gray-100)}.filament-spatie-health .bg-red-100{background-color:var(--color-red-100)}.filament-spatie-health .bg-white{background-color:var(--color-white)}.filament-spatie-health .bg-yellow-100{background-color:var(--color-yellow-100)}.filament-spatie-health .p-2{padding:calc(var(--spacing)*2)}.filament-spatie-health .px-4{padding-inline:calc(var(--spacing)*4)}.filament-spatie-health .py-5{padding-block:calc(var(--spacing)*5)}.filament-spatie-health .text-center{text-align:center}.filament-spatie-health .text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.filament-spatie-health .font-bold{--tw-font-weight:var(--font-weight-bold);font-weight:var(--font-weight-bold)}.filament-spatie-health .font-medium{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.filament-spatie-health .text-blue-500{color:var(--color-blue-500)}.filament-spatie-health .text-emerald-500{color:var(--color-emerald-500)}.filament-spatie-health .text-gray-400{color:var(--color-gray-400)}.filament-spatie-health .text-gray-500{color:var(--color-gray-500)}.filament-spatie-health .text-gray-600{color:var(--color-gray-600)}.filament-spatie-health .text-gray-900{color:var(--color-gray-900)}.filament-spatie-health .text-red-500{color:var(--color-red-500)}.filament-spatie-health .text-yellow-500{color:var(--color-yellow-500)}.filament-spatie-health .shadow-xs{--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.filament-spatie-health .shadow-gray-200{--tw-shadow-color:oklch(92.8% .006 264.531)}@supports (color:color-mix(in lab, red, red)){.filament-spatie-health .shadow-gray-200{--tw-shadow-color:color-mix(in oklab,var(--color-gray-200)var(--tw-shadow-alpha),transparent)}}.filament-spatie-health .transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}@media (min-width:40rem){.filament-spatie-health .sm\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.filament-spatie-health .sm\:p-6{padding:calc(var(--spacing)*6)}}@media (min-width:48rem){.filament-spatie-health .md\:mt-1{margin-top:calc(var(--spacing)*1)}.filament-spatie-health .md\:min-h-\[130px\]{min-height:130px}:where(.filament-spatie-health .md\:space-x-3>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*3)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*3)*calc(1 - var(--tw-space-x-reverse)))}.filament-spatie-health .md\:text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}}@media (min-width:64rem){.filament-spatie-health .lg\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}}.filament-spatie-health .dark\:border-t:is(.dark *){border-top-style:var(--tw-border-style);border-top-width:1px}.filament-spatie-health .dark\:border-gray-700:is(.dark *){border-color:var(--color-gray-700)}.filament-spatie-health .dark\:bg-gray-800:is(.dark *){background-color:var(--color-gray-800)}.filament-spatie-health .dark\:text-gray-200:is(.dark *){color:var(--color-gray-200)}.filament-spatie-health .dark\:text-gray-400:is(.dark *){color:var(--color-gray-400)}.filament-spatie-health .dark\:text-white:is(.dark *){color:var(--color-white)}.filament-spatie-health .dark\:shadow-md:is(.dark *){--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.filament-spatie-health .dark\:shadow-black\/25:is(.dark *){--tw-shadow-color:#00000040}@supports (color:color-mix(in lab, red, red)){.filament-spatie-health .dark\:shadow-black\/25:is(.dark *){--tw-shadow-color:color-mix(in oklab,color-mix(in oklab,var(--color-black)25%,transparent)var(--tw-shadow-alpha),transparent)}}}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-space-x-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-font-weight{syntax:"*";inherits:false}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}