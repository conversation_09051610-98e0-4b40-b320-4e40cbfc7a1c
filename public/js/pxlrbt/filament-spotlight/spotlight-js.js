(()=>{var e={27:(e,t,n)=>{"use strict";function s(e){return Array.isArray?Array.isArray(e):"[object Array]"===l(e)}n.r(t);function i(e){return"string"==typeof e}function r(e){return"number"==typeof e}function c(e){return!0===e||!1===e||function(e){return o(e)&&null!==e}(e)&&"[object Boolean]"==l(e)}function o(e){return"object"==typeof e}function h(e){return null!=e}function a(e){return!e.trim().length}function l(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}const u=Object.prototype.hasOwnProperty;class d{constructor(e){this._keys=[],this._keyMap={};let t=0;e.forEach((e=>{let n=p(e);t+=n.weight,this._keys.push(n),this._keyMap[n.id]=n,t+=n.weight})),this._keys.forEach((e=>{e.weight/=t}))}get(e){return this._keyMap[e]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function p(e){let t=null,n=null,r=null,c=1,o=null;if(i(e)||s(e))r=e,t=g(e),n=f(e);else{if(!u.call(e,"name"))throw new Error((e=>`Missing ${e} property in key`)("name"));const s=e.name;if(r=s,u.call(e,"weight")&&(c=e.weight,c<=0))throw new Error((e=>`Property 'weight' in key '${e}' must be a positive integer`)(s));t=g(s),n=f(s),o=e.getFn}return{path:t,id:n,weight:c,src:r,getFn:o}}function g(e){return s(e)?e:e.split(".")}function f(e){return s(e)?e.join("."):e}var m={isCaseSensitive:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(e,t)=>e.score===t.score?e.idx<t.idx?-1:1:e.score<t.score?-1:1,includeMatches:!1,findAllMatches:!1,minMatchCharLength:1,location:0,threshold:.6,distance:100,...{useExtendedSearch:!1,getFn:function(e,t){let n=[],o=!1;const a=(e,t,l)=>{if(h(e))if(t[l]){const u=e[t[l]];if(!h(u))return;if(l===t.length-1&&(i(u)||r(u)||c(u)))n.push(function(e){return null==e?"":function(e){if("string"==typeof e)return e;let t=e+"";return"0"==t&&1/e==-1/0?"-0":t}(e)}(u));else if(s(u)){o=!0;for(let e=0,n=u.length;e<n;e+=1)a(u[e],t,l+1)}else t.length&&a(u,t,l+1)}else n.push(e)};return a(e,i(t)?t.split("."):t,0),o?n:n[0]},ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1}};const y=/[^ ]+/g;class M{constructor({getFn:e=m.getFn,fieldNormWeight:t=m.fieldNormWeight}={}){this.norm=function(e=1,t=3){const n=new Map,s=Math.pow(10,t);return{get(t){const i=t.match(y).length;if(n.has(i))return n.get(i);const r=1/Math.pow(i,.5*e),c=parseFloat(Math.round(r*s)/s);return n.set(i,c),c},clear(){n.clear()}}}(t,3),this.getFn=e,this.isCreated=!1,this.setIndexRecords()}setSources(e=[]){this.docs=e}setIndexRecords(e=[]){this.records=e}setKeys(e=[]){this.keys=e,this._keysMap={},e.forEach(((e,t)=>{this._keysMap[e.id]=t}))}create(){!this.isCreated&&this.docs.length&&(this.isCreated=!0,i(this.docs[0])?this.docs.forEach(((e,t)=>{this._addString(e,t)})):this.docs.forEach(((e,t)=>{this._addObject(e,t)})),this.norm.clear())}add(e){const t=this.size();i(e)?this._addString(e,t):this._addObject(e,t)}removeAt(e){this.records.splice(e,1);for(let t=e,n=this.size();t<n;t+=1)this.records[t].i-=1}getValueForItemAtKeyId(e,t){return e[this._keysMap[t]]}size(){return this.records.length}_addString(e,t){if(!h(e)||a(e))return;let n={v:e,i:t,n:this.norm.get(e)};this.records.push(n)}_addObject(e,t){let n={i:t,$:{}};this.keys.forEach(((t,r)=>{let c=t.getFn?t.getFn(e):this.getFn(e,t.path);if(h(c))if(s(c)){let e=[];const t=[{nestedArrIndex:-1,value:c}];for(;t.length;){const{nestedArrIndex:n,value:r}=t.pop();if(h(r))if(i(r)&&!a(r)){let t={v:r,i:n,n:this.norm.get(r)};e.push(t)}else s(r)&&r.forEach(((e,n)=>{t.push({nestedArrIndex:n,value:e})}))}n.$[r]=e}else if(i(c)&&!a(c)){let e={v:c,n:this.norm.get(c)};n.$[r]=e}})),this.records.push(n)}toJSON(){return{keys:this.keys,records:this.records}}}function x(e,t,{getFn:n=m.getFn,fieldNormWeight:s=m.fieldNormWeight}={}){const i=new M({getFn:n,fieldNormWeight:s});return i.setKeys(e.map(p)),i.setSources(t),i.create(),i}function v(e,{errors:t=0,currentLocation:n=0,expectedLocation:s=0,distance:i=m.distance,ignoreLocation:r=m.ignoreLocation}={}){const c=t/e.length;if(r)return c;const o=Math.abs(s-n);return i?c+o/i:o?1:c}const L=32;function S(e,t,n,{location:s=m.location,distance:i=m.distance,threshold:r=m.threshold,findAllMatches:c=m.findAllMatches,minMatchCharLength:o=m.minMatchCharLength,includeMatches:h=m.includeMatches,ignoreLocation:a=m.ignoreLocation}={}){if(t.length>L)throw new Error(`Pattern length exceeds max of ${L}.`);const l=t.length,u=e.length,d=Math.max(0,Math.min(s,u));let p=r,g=d;const f=o>1||h,y=f?Array(u):[];let M;for(;(M=e.indexOf(t,g))>-1;){let e=v(t,{currentLocation:M,expectedLocation:d,distance:i,ignoreLocation:a});if(p=Math.min(e,p),g=M+l,f){let e=0;for(;e<l;)y[M+e]=1,e+=1}}g=-1;let x=[],S=1,w=l+u;const k=1<<l-1;for(let s=0;s<l;s+=1){let r=0,o=w;for(;r<o;){v(t,{errors:s,currentLocation:d+o,expectedLocation:d,distance:i,ignoreLocation:a})<=p?r=o:w=o,o=Math.floor((w-r)/2+r)}w=o;let h=Math.max(1,d-o+1),m=c?u:Math.min(d+o,u)+l,M=Array(m+2);M[m+1]=(1<<s)-1;for(let r=m;r>=h;r-=1){let c=r-1,o=n[e.charAt(c)];if(f&&(y[c]=+!!o),M[r]=(M[r+1]<<1|1)&o,s&&(M[r]|=(x[r+1]|x[r])<<1|1|x[r+1]),M[r]&k&&(S=v(t,{errors:s,currentLocation:c,expectedLocation:d,distance:i,ignoreLocation:a}),S<=p)){if(p=S,g=c,g<=d)break;h=Math.max(1,2*d-g)}}if(v(t,{errors:s+1,currentLocation:d,expectedLocation:d,distance:i,ignoreLocation:a})>p)break;x=M}const I={isMatch:g>=0,score:Math.max(.001,S)};if(f){const e=function(e=[],t=m.minMatchCharLength){let n=[],s=-1,i=-1,r=0;for(let c=e.length;r<c;r+=1){let c=e[r];c&&-1===s?s=r:c||-1===s||(i=r-1,i-s+1>=t&&n.push([s,i]),s=-1)}return e[r-1]&&r-s>=t&&n.push([s,r-1]),n}(y,o);e.length?h&&(I.indices=e):I.isMatch=!1}return I}function w(e){let t={};for(let n=0,s=e.length;n<s;n+=1){const i=e.charAt(n);t[i]=(t[i]||0)|1<<s-n-1}return t}class k{constructor(e,{location:t=m.location,threshold:n=m.threshold,distance:s=m.distance,includeMatches:i=m.includeMatches,findAllMatches:r=m.findAllMatches,minMatchCharLength:c=m.minMatchCharLength,isCaseSensitive:o=m.isCaseSensitive,ignoreLocation:h=m.ignoreLocation}={}){if(this.options={location:t,threshold:n,distance:s,includeMatches:i,findAllMatches:r,minMatchCharLength:c,isCaseSensitive:o,ignoreLocation:h},this.pattern=o?e:e.toLowerCase(),this.chunks=[],!this.pattern.length)return;const a=(e,t)=>{this.chunks.push({pattern:e,alphabet:w(e),startIndex:t})},l=this.pattern.length;if(l>L){let e=0;const t=l%L,n=l-t;for(;e<n;)a(this.pattern.substr(e,L),e),e+=L;if(t){const e=l-L;a(this.pattern.substr(e),e)}}else a(this.pattern,0)}searchIn(e){const{isCaseSensitive:t,includeMatches:n}=this.options;if(t||(e=e.toLowerCase()),this.pattern===e){let t={isMatch:!0,score:0};return n&&(t.indices=[[0,e.length-1]]),t}const{location:s,distance:i,threshold:r,findAllMatches:c,minMatchCharLength:o,ignoreLocation:h}=this.options;let a=[],l=0,u=!1;this.chunks.forEach((({pattern:t,alphabet:d,startIndex:p})=>{const{isMatch:g,score:f,indices:m}=S(e,t,d,{location:s+p,distance:i,threshold:r,findAllMatches:c,minMatchCharLength:o,includeMatches:n,ignoreLocation:h});g&&(u=!0),l+=f,g&&m&&(a=[...a,...m])}));let d={isMatch:u,score:u?l/this.chunks.length:1};return u&&n&&(d.indices=a),d}}class I{constructor(e){this.pattern=e}static isMultiMatch(e){return _(e,this.multiRegex)}static isSingleMatch(e){return _(e,this.singleRegex)}search(){}}function _(e,t){const n=e.match(t);return n?n[1]:null}class C extends I{constructor(e,{location:t=m.location,threshold:n=m.threshold,distance:s=m.distance,includeMatches:i=m.includeMatches,findAllMatches:r=m.findAllMatches,minMatchCharLength:c=m.minMatchCharLength,isCaseSensitive:o=m.isCaseSensitive,ignoreLocation:h=m.ignoreLocation}={}){super(e),this._bitapSearch=new k(e,{location:t,threshold:n,distance:s,includeMatches:i,findAllMatches:r,minMatchCharLength:c,isCaseSensitive:o,ignoreLocation:h})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(e){return this._bitapSearch.searchIn(e)}}class $ extends I{constructor(e){super(e)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(e){let t,n=0;const s=[],i=this.pattern.length;for(;(t=e.indexOf(this.pattern,n))>-1;)n=t+i,s.push([t,n-1]);const r=!!s.length;return{isMatch:r,score:r?0:1,indices:s}}}const b=[class extends I{constructor(e){super(e)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(e){const t=e===this.pattern;return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}},$,class extends I{constructor(e){super(e)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(e){const t=e.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}},class extends I{constructor(e){super(e)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(e){const t=!e.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,e.length-1]}}},class extends I{constructor(e){super(e)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(e){const t=!e.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,e.length-1]}}},class extends I{constructor(e){super(e)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(e){const t=e.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[e.length-this.pattern.length,e.length-1]}}},class extends I{constructor(e){super(e)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(e){const t=-1===e.indexOf(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,e.length-1]}}},C],E=b.length,A=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/;const O=new Set([C.type,$.type]);class R{constructor(e,{isCaseSensitive:t=m.isCaseSensitive,includeMatches:n=m.includeMatches,minMatchCharLength:s=m.minMatchCharLength,ignoreLocation:i=m.ignoreLocation,findAllMatches:r=m.findAllMatches,location:c=m.location,threshold:o=m.threshold,distance:h=m.distance}={}){this.query=null,this.options={isCaseSensitive:t,includeMatches:n,minMatchCharLength:s,findAllMatches:r,ignoreLocation:i,location:c,threshold:o,distance:h},this.pattern=t?e:e.toLowerCase(),this.query=function(e,t={}){return e.split("|").map((e=>{let n=e.trim().split(A).filter((e=>e&&!!e.trim())),s=[];for(let e=0,i=n.length;e<i;e+=1){const i=n[e];let r=!1,c=-1;for(;!r&&++c<E;){const e=b[c];let n=e.isMultiMatch(i);n&&(s.push(new e(n,t)),r=!0)}if(!r)for(c=-1;++c<E;){const e=b[c];let n=e.isSingleMatch(i);if(n){s.push(new e(n,t));break}}}return s}))}(this.pattern,this.options)}static condition(e,t){return t.useExtendedSearch}searchIn(e){const t=this.query;if(!t)return{isMatch:!1,score:1};const{includeMatches:n,isCaseSensitive:s}=this.options;e=s?e:e.toLowerCase();let i=0,r=[],c=0;for(let s=0,o=t.length;s<o;s+=1){const o=t[s];r.length=0,i=0;for(let t=0,s=o.length;t<s;t+=1){const s=o[t],{isMatch:h,indices:a,score:l}=s.search(e);if(!h){c=0,i=0,r.length=0;break}if(i+=1,c+=l,n){const e=s.constructor.type;O.has(e)?r=[...r,...a]:r.push(a)}}if(i){let e={isMatch:!0,score:c/i};return n&&(e.indices=r),e}}return{isMatch:!1,score:1}}}const F=[];function N(e,t){for(let n=0,s=F.length;n<s;n+=1){let s=F[n];if(s.condition(e,t))return new s(e,t)}return new k(e,t)}const D="$and",W="$or",j="$path",P="$val",q=e=>!(!e[D]&&!e[W]),T=e=>({[D]:Object.keys(e).map((t=>({[t]:e[t]})))});function z(e,t,{auto:n=!0}={}){const r=e=>{let c=Object.keys(e);const h=(e=>!!e[j])(e);if(!h&&c.length>1&&!q(e))return r(T(e));if((e=>!s(e)&&o(e)&&!q(e))(e)){const s=h?e[j]:c[0],r=h?e[P]:e[s];if(!i(r))throw new Error((e=>`Invalid value for key ${e}`)(s));const o={keyId:f(s),pattern:r};return n&&(o.searcher=N(r,t)),o}let a={children:[],operator:c[0]};return c.forEach((t=>{const n=e[t];s(n)&&n.forEach((e=>{a.children.push(r(e))}))})),a};return q(e)||(e=T(e)),r(e)}function J(e,t){const n=e.matches;t.matches=[],h(n)&&n.forEach((e=>{if(!h(e.indices)||!e.indices.length)return;const{indices:n,value:s}=e;let i={indices:n,value:s};e.key&&(i.key=e.key.src),e.idx>-1&&(i.refIndex=e.idx),t.matches.push(i)}))}function K(e,t){t.score=e.score}class Q{constructor(e,t={},n){this.options={...m,...t},this.options.useExtendedSearch,this._keyStore=new d(this.options.keys),this.setCollection(e,n)}setCollection(e,t){if(this._docs=e,t&&!(t instanceof M))throw new Error("Incorrect 'index' type");this._myIndex=t||x(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(e){h(e)&&(this._docs.push(e),this._myIndex.add(e))}remove(e=(()=>!1)){const t=[];for(let n=0,s=this._docs.length;n<s;n+=1){const i=this._docs[n];e(i,n)&&(this.removeAt(n),n-=1,s-=1,t.push(i))}return t}removeAt(e){this._docs.splice(e,1),this._myIndex.removeAt(e)}getIndex(){return this._myIndex}search(e,{limit:t=-1}={}){const{includeMatches:n,includeScore:s,shouldSort:c,sortFn:o,ignoreFieldNorm:h}=this.options;let a=i(e)?i(this._docs[0])?this._searchStringList(e):this._searchObjectList(e):this._searchLogical(e);return function(e,{ignoreFieldNorm:t=m.ignoreFieldNorm}){e.forEach((e=>{let n=1;e.matches.forEach((({key:e,norm:s,score:i})=>{const r=e?e.weight:null;n*=Math.pow(0===i&&r?Number.EPSILON:i,(r||1)*(t?1:s))})),e.score=n}))}(a,{ignoreFieldNorm:h}),c&&a.sort(o),r(t)&&t>-1&&(a=a.slice(0,t)),function(e,t,{includeMatches:n=m.includeMatches,includeScore:s=m.includeScore}={}){const i=[];return n&&i.push(J),s&&i.push(K),e.map((e=>{const{idx:n}=e,s={item:t[n],refIndex:n};return i.length&&i.forEach((t=>{t(e,s)})),s}))}(a,this._docs,{includeMatches:n,includeScore:s})}_searchStringList(e){const t=N(e,this.options),{records:n}=this._myIndex,s=[];return n.forEach((({v:e,i:n,n:i})=>{if(!h(e))return;const{isMatch:r,score:c,indices:o}=t.searchIn(e);r&&s.push({item:e,idx:n,matches:[{score:c,value:e,norm:i,indices:o}]})})),s}_searchLogical(e){const t=z(e,this.options),n=(e,t,s)=>{if(!e.children){const{keyId:n,searcher:i}=e,r=this._findMatches({key:this._keyStore.get(n),value:this._myIndex.getValueForItemAtKeyId(t,n),searcher:i});return r&&r.length?[{idx:s,item:t,matches:r}]:[]}const i=[];for(let r=0,c=e.children.length;r<c;r+=1){const c=e.children[r],o=n(c,t,s);if(o.length)i.push(...o);else if(e.operator===D)return[]}return i},s=this._myIndex.records,i={},r=[];return s.forEach((({$:e,i:s})=>{if(h(e)){let c=n(t,e,s);c.length&&(i[s]||(i[s]={idx:s,item:e,matches:[]},r.push(i[s])),c.forEach((({matches:e})=>{i[s].matches.push(...e)})))}})),r}_searchObjectList(e){const t=N(e,this.options),{keys:n,records:s}=this._myIndex,i=[];return s.forEach((({$:e,i:s})=>{if(!h(e))return;let r=[];n.forEach(((n,s)=>{r.push(...this._findMatches({key:n,value:e[s],searcher:t}))})),r.length&&i.push({idx:s,item:e,matches:r})})),i}_findMatches({key:e,value:t,searcher:n}){if(!h(t))return[];let i=[];if(s(t))t.forEach((({v:t,i:s,n:r})=>{if(!h(t))return;const{isMatch:c,score:o,indices:a}=n.searchIn(t);c&&i.push({score:o,key:e,value:t,idx:s,norm:r,indices:a})}));else{const{v:s,n:r}=t,{isMatch:c,score:o,indices:h}=n.searchIn(s);c&&i.push({score:o,key:e,value:s,norm:r,indices:h})}return i}}Q.version="6.6.2",Q.createIndex=x,Q.parseIndex=function(e,{getFn:t=m.getFn,fieldNormWeight:n=m.fieldNormWeight}={}){const{keys:s,records:i}=e,r=new M({getFn:t,fieldNormWeight:n});return r.setKeys(s),r.setIndexRecords(i),r},Q.config=m,Q.parseQuery=z,function(...e){F.push(...e)}(R),window.LivewireUISpotlight=function(e){return{inputPlaceholder:e.placeholder,searchEngine:"commands",commands:e.commands,commandSearch:null,selectedCommand:null,dependencySearch:null,dependencyQueryResults:window.Livewire.find(e.componentId).entangle("dependencyQueryResults"),requiredDependencies:[],currentDependency:null,resolvedDependencies:{},showResultsWithoutInput:e.showResultsWithoutInput,init:function(){var t=this;this.commandSearch=new Q(this.commands,{threshold:.3,keys:["name","description","synonyms"]}),this.dependencySearch=new Q([],{threshold:.3,keys:["name","description","synonyms"]}),this.$watch("dependencyQueryResults",(function(e){t.dependencySearch.setCollection(e)})),this.$watch("input",(function(e){0===e.length&&(t.selected=0),null!==t.selectedCommand&&null!==t.currentDependency&&"search"===t.currentDependency.type&&t.$wire.searchDependency(t.selectedCommand.id,t.currentDependency.id,e,t.resolvedDependencies)})),this.$watch("isOpen",(function(n){!1===n&&setTimeout((function(){t.input="",t.inputPlaceholder=e.placeholder,t.searchEngine="commands",t.resolvedDependencies={},t.selectedCommand=null,t.currentDependency=null,t.selectedCommand=null,t.requiredDependencies=[]}),300)}))},isOpen:!1,toggleOpen:function(){var e=this;this.isOpen?this.isOpen=!1:(this.input="",this.isOpen=!0,setTimeout((function(){e.$refs.input.focus()}),100))},input:"",filteredItems:function(){return"commands"===this.searchEngine?!this.input&&this.showResultsWithoutInput?this.commandSearch.getIndex().docs.map((function(e,t){return[{item:e},t]})):this.commandSearch.search(this.input).map((function(e,t){return[e,t]})):"search"===this.searchEngine?!this.input&&this.showResultsWithoutInput?this.dependencySearch.getIndex().docs.map((function(e,t){return[{item:e},t]})):this.dependencySearch.search(this.input).map((function(e,t){return[e,t]})):[]},selectUp:function(){var e=this;this.selected=Math.max(0,this.selected-1),this.$nextTick((function(){e.$refs.results.children[e.selected+1].scrollIntoView({block:"nearest"})}))},selectDown:function(){var e=this;this.selected=Math.min(this.filteredItems().length-1,this.selected+1),this.$nextTick((function(){e.$refs.results.children[e.selected+1].scrollIntoView({block:"nearest"})}))},go:function(e){var t,n=this;(null===this.selectedCommand&&(this.selectedCommand=this.commands.find((function(t){return t.id===(e||n.filteredItems()[n.selected][0].item.id)})),this.requiredDependencies=JSON.parse(JSON.stringify(this.selectedCommand.dependencies))),null!==this.currentDependency)&&(t="search"===this.currentDependency.type?e||this.filteredItems()[this.selected][0].item.id:this.input,this.resolvedDependencies[this.currentDependency.id]=t);this.requiredDependencies.length>0?(this.input="",this.currentDependency=this.requiredDependencies.pop(),this.inputPlaceholder=this.currentDependency.placeholder,this.searchEngine="search"===this.currentDependency.type&&"search"):(this.isOpen=!1,this.$wire.execute(this.selectedCommand.id,this.resolvedDependencies))},selected:0}}}},t={};function n(s){var i=t[s];if(void 0!==i)return i.exports;var r=t[s]={exports:{}};return e[s](r,r.exports,n),r.exports}n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n(27)})();