# 1. Spatie Media Library Implementation Guide

*Refactored from: .ai/guides/chinook/packages/120-spatie-media-library-guide.md on 2025-07-11*

## 1.1 Table of Contents

- [1.2 Overview](#12-overview)
- [1.3 Installation & Configuration](#13-installation--configuration)
- [1.4 Basic Media Implementation](#14-basic-media-implementation)
- [1.5 Advanced Media Patterns](#15-advanced-media-patterns)
- [1.6 Chinook Integration](#16-chinook-integration)
- [1.7 File Conversions & Processing](#17-file-conversions--processing)
- [1.8 Performance Optimization](#18-performance-optimization)
- [1.9 CDN Integration](#19-cdn-integration)
- [1.10 Testing Strategies](#110-testing-strategies)
- [1.11 Production Deployment](#111-production-deployment)
- [1.12 Best Practices](#112-best-practices)

## 1.2 Overview

Spatie Laravel Media Library provides comprehensive file management capabilities for Laravel applications. This guide demonstrates enterprise-grade implementation patterns for the Chinook music database with audio file management, image processing, CDN integration, and taxonomy-based media organization using Laravel 12 modern syntax and the aliziodev/laravel-taxonomy package.

### 1.2.1 Key Features

- **Multi-format Support**: Audio files, images, documents, and videos
- **Automatic Conversions**: Image resizing, audio format conversion
- **CDN Integration**: Seamless cloud storage and delivery
- **Performance Optimized**: Efficient file handling and caching
- **Laravel 12 Compatible**: Modern syntax with casts() method patterns
- **Enterprise Ready**: Production-ready scaling and security
- **Taxonomy Integration**: Media organization using aliziodev/laravel-taxonomy

### 1.2.2 Architecture Overview

**Accessibility Note:** This architecture diagram shows the integration between Spatie Media Library and the Chinook system, including media models, file types, storage systems, and taxonomy-based organization. The diagram uses WCAG 2.1 AA compliant colors with high contrast ratios for optimal accessibility.

```mermaid
---
title: Media Library Architecture - Chinook Integration with Taxonomy System
---
graph TB
    subgraph "Media System"
        A[Media Model]
        B[HasMedia Trait]
        C[MediaCollection]
        D[MediaConversion]
    end
    
    subgraph "Chinook Models"
        E[ChinookArtist Model]
        F[ChinookAlbum Model]
        G[ChinookTrack Model]
        H[ChinookPlaylist Model]
    end
    
    subgraph "Taxonomy System"
        T[Taxonomies]
        TM[Terms]
        TR[Termables]
    end
    
    subgraph "File Types"
        I[Audio Files]
        J[Cover Images]
        K[Artist Photos]
        L[Documents]
    end
    
    subgraph "Storage"
        M[Local Storage]
        N[S3/CDN]
        O[Conversions]
    end
    
    B --> A
    A --> C
    A --> D
    E --> B
    F --> B
    G --> B
    H --> B
    
    E --> TR
    F --> TR
    G --> TR
    H --> TR
    TR --> TM
    TM --> T
    
    I --> A
    J --> A
    K --> A
    L --> A
    
    A --> M
    A --> N
    D --> O
    
    style A fill:#1976d2,stroke:#0d47a1,stroke-width:2px,color:#ffffff
    style B fill:#388e3c,stroke:#1b5e20,stroke-width:2px,color:#ffffff
    style T fill:#f57c00,stroke:#e65100,stroke-width:2px,color:#ffffff
    style TM fill:#f57c00,stroke:#e65100,stroke-width:2px,color:#ffffff
    style TR fill:#f57c00,stroke:#e65100,stroke-width:2px,color:#ffffff
    style I fill:#d32f2f,stroke:#b71c1c,stroke-width:2px,color:#ffffff
    style N fill:#37474f,stroke:#263238,stroke-width:2px,color:#ffffff
```

## 1.3 Installation & Configuration

### 1.3.1 Package Installation

```bash
# Install Spatie Media Library
composer require spatie/laravel-medialibrary

# Install taxonomy package (if not already installed)
composer require aliziodev/laravel-taxonomy

# Publish and run migrations
php artisan vendor:publish --provider="Spatie\MediaLibrary\MediaLibraryServiceProvider" --tag="medialibrary-migrations"
php artisan migrate

# Publish configuration (optional)
php artisan vendor:publish --provider="Spatie\MediaLibrary\MediaLibraryServiceProvider" --tag="medialibrary-config"
```

### 1.3.2 Configuration Setup

```php
// config/media-library.php
return [
    'disk_name' => env('MEDIA_DISK', 'public'),
    
    'max_file_size' => 1024 * 1024 * 10, // 10MB
    
    'queue_name' => env('MEDIA_QUEUE', 'default'),
    
    'queue_conversions_by_default' => env('QUEUE_CONVERSIONS', true),
    
    'media_model' => Spatie\MediaLibrary\MediaCollections\Models\Media::class,
    
    'temporary_directory_path' => storage_path('app/temp'),
    
    'generate_thumbnails_for_temporary_uploads' => true,
    
    'file_namer' => Spatie\MediaLibrary\Support\FileNamer\DefaultFileNamer::class,
    
    'path_generator' => Spatie\MediaLibrary\Support\PathGenerator\DefaultPathGenerator::class,
    
    'url_generator' => Spatie\MediaLibrary\Support\UrlGenerator\DefaultUrlGenerator::class,
    
    'version_urls' => false,
    
    'image_optimizers' => [
        Spatie\ImageOptimizer\Optimizers\Jpegoptim::class => [
            '-m85',
            '--strip-all',
            '--all-progressive',
        ],
        Spatie\ImageOptimizer\Optimizers\Pngquant::class => [
            '--force',
        ],
        Spatie\ImageOptimizer\Optimizers\Optipng::class => [
            '-i0',
            '-o2',
            '-quiet',
        ],
    ],
    
    'image_generators' => [
        Spatie\MediaLibrary\Conversions\ImageGenerators\Image::class,
        Spatie\MediaLibrary\Conversions\ImageGenerators\Webp::class,
        Spatie\MediaLibrary\Conversions\ImageGenerators\Pdf::class,
        Spatie\MediaLibrary\Conversions\ImageGenerators\Svg::class,
        Spatie\MediaLibrary\Conversions\ImageGenerators\Video::class,
    ],
    
    'temporary_upload_path_generator' => null,
    
    'enable_vapor_uploads' => env('ENABLE_VAPOR_UPLOADS', false),
    
    'remote_collections' => [],
];
```

### 1.3.3 Environment Configuration

```bash
# .env configuration
MEDIA_DISK=public
MEDIA_QUEUE=media
QUEUE_CONVERSIONS=true

# AWS S3 Configuration (for production)
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=chinook-media

# CDN Configuration
CDN_URL=https://cdn.chinook-music.com
```

## 1.4 Basic Media Implementation

### 1.4.1 Model Setup with Taxonomy Integration

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Aliziodev\LaravelTaxonomy\Traits\HasTaxonomies;
use App\Traits\HasUserStamps;
use App\Traits\HasSecondaryUniqueKey;
use App\Traits\HasSlug;

class ChinookArtist extends Model implements HasMedia
{
    use SoftDeletes, InteractsWithMedia, HasTaxonomies, HasUserStamps, HasSecondaryUniqueKey, HasSlug;

    protected $table = 'chinook_artists';

    protected $fillable = [
        'name',
        'biography',
        'website',
        'is_active',
        'metadata',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'metadata' => 'array',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    public function albums(): HasMany
    {
        return $this->hasMany(ChinookAlbum::class, 'artist_id');
    }

    /**
     * Register media collections
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('profile_photos')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp'])
            ->singleFile();

        $this->addMediaCollection('gallery')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp']);

        $this->addMediaCollection('documents')
            ->acceptsMimeTypes(['application/pdf', 'text/plain']);
    }

    /**
     * Register media conversions
     */
    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(300)
            ->height(300)
            ->sharpen(10)
            ->optimize()
            ->nonQueued();

        $this->addMediaConversion('preview')
            ->width(800)
            ->height(600)
            ->optimize()
            ->performOnCollections('profile_photos', 'gallery');

        $this->addMediaConversion('webp')
            ->format('webp')
            ->width(800)
            ->height(600)
            ->optimize()
            ->performOnCollections('profile_photos', 'gallery');
    }

    /**
     * Get artist genres using taxonomy system
     */
    public function getGenresAttribute(): Collection
    {
        return $this->getTermsByTaxonomy('Genres');
    }

    /**
     * Get profile photo with fallback
     */
    public function getProfilePhotoAttribute(): ?string
    {
        $media = $this->getFirstMedia('profile_photos');
        return $media ? $media->getUrl('preview') : null;
    }

    /**
     * Get profile photo thumbnail
     */
    public function getProfileThumbnailAttribute(): ?string
    {
        $media = $this->getFirstMedia('profile_photos');
        return $media ? $media->getUrl('thumb') : null;
    }
}
```

### 1.4.2 Album Model with Media Integration

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Aliziodev\LaravelTaxonomy\Traits\HasTaxonomies;

class ChinookAlbum extends Model implements HasMedia
{
    use SoftDeletes, InteractsWithMedia, HasTaxonomies;

    protected $table = 'chinook_albums';

    protected function casts(): array
    {
        return [
            'release_date' => 'date',
            'is_active' => 'boolean',
            'metadata' => 'array',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    public function artist(): BelongsTo
    {
        return $this->belongsTo(ChinookArtist::class, 'artist_id');
    }

    public function tracks(): HasMany
    {
        return $this->hasMany(ChinookTrack::class, 'album_id');
    }

    /**
     * Register media collections
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('cover_art')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp'])
            ->singleFile();

        $this->addMediaCollection('liner_notes')
            ->acceptsMimeTypes(['application/pdf', 'text/plain']);

        $this->addMediaCollection('promotional_materials')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'application/pdf']);
    }

    /**
     * Register media conversions
     */
    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(200)
            ->height(200)
            ->sharpen(10)
            ->optimize()
            ->nonQueued();

        $this->addMediaConversion('medium')
            ->width(400)
            ->height(400)
            ->optimize()
            ->performOnCollections('cover_art');

        $this->addMediaConversion('large')
            ->width(800)
            ->height(800)
            ->optimize()
            ->performOnCollections('cover_art');

        $this->addMediaConversion('webp_thumb')
            ->format('webp')
            ->width(200)
            ->height(200)
            ->optimize()
            ->performOnCollections('cover_art');
    }

    /**
     * Get cover art with fallback
     */
    public function getCoverArtAttribute(): ?string
    {
        $media = $this->getFirstMedia('cover_art');
        return $media ? $media->getUrl('large') : null;
    }

    /**
     * Get cover art thumbnail
     */
    public function getCoverThumbnailAttribute(): ?string
    {
        $media = $this->getFirstMedia('cover_art');
        return $media ? $media->getUrl('thumb') : null;
    }
}
```

## 1.5 Advanced Media Patterns

### 1.5.1 Custom Media Collections with Taxonomy

```php
<?php

namespace App\Services;

use App\Models\ChinookArtist;
use App\Models\ChinookAlbum;
use App\Models\ChinookTrack;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Aliziodev\LaravelTaxonomy\Models\Term;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Collection;

class MediaManagementService
{
    /**
     * Upload artist profile photo with taxonomy tagging
     */
    public function uploadArtistPhoto(ChinookArtist $artist, UploadedFile $file, array $taxonomyTerms = []): Media
    {
        $media = $artist->addMediaFromRequest('file')
            ->toMediaCollection('profile_photos');

        // Attach taxonomy terms to the media
        if (!empty($taxonomyTerms)) {
            $this->attachTermsToMedia($media, $taxonomyTerms);
        }

        return $media;
    }

    /**
     * Upload album cover art with automatic genre detection
     */
    public function uploadAlbumCover(ChinookAlbum $album, UploadedFile $file): Media
    {
        $media = $album->addMediaFromRequest('file')
            ->usingName($album->title . ' - Cover Art')
            ->usingFileName($album->slug . '-cover.' . $file->getClientOriginalExtension())
            ->toMediaCollection('cover_art');

        // Inherit genre terms from album
        $albumGenres = $album->getTermsByTaxonomy('Genres');
        if ($albumGenres->isNotEmpty()) {
            $this->attachTermsToMedia($media, $albumGenres->pluck('id')->toArray());
        }

        return $media;
    }

    /**
     * Attach taxonomy terms to media
     */
    private function attachTermsToMedia(Media $media, array $termIds): void
    {
        $terms = Term::whereIn('id', $termIds)->get();

        foreach ($terms as $term) {
            $media->attachTerm($term);
        }
    }

    /**
     * Get media by taxonomy terms
     */
    public function getMediaByTaxonomy(string $taxonomyName, string $termName): Collection
    {
        return Media::whereHasTerm($termName, $taxonomyName)->get();
    }
}
```

## 1.6 Chinook Integration

### 1.6.1 Media Upload Controller

```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ChinookArtist;
use App\Models\ChinookAlbum;
use App\Services\MediaManagementService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class MediaUploadController extends Controller
{
    public function __construct(
        private MediaManagementService $mediaService
    ) {}

    /**
     * Upload artist profile photo
     */
    public function uploadArtistPhoto(Request $request, ChinookArtist $artist): JsonResponse
    {
        $request->validate([
            'file' => 'required|image|max:10240', // 10MB max
            'taxonomy_terms' => 'array',
            'taxonomy_terms.*' => 'exists:terms,id',
        ]);

        $media = $this->mediaService->uploadArtistPhoto(
            $artist,
            $request->file('file'),
            $request->input('taxonomy_terms', [])
        );

        return response()->json([
            'message' => 'Photo uploaded successfully',
            'media' => [
                'id' => $media->id,
                'url' => $media->getUrl(),
                'thumb_url' => $media->getUrl('thumb'),
                'preview_url' => $media->getUrl('preview'),
            ]
        ]);
    }

    /**
     * Upload album cover art
     */
    public function uploadAlbumCover(Request $request, ChinookAlbum $album): JsonResponse
    {
        $request->validate([
            'file' => 'required|image|max:10240',
        ]);

        $media = $this->mediaService->uploadAlbumCover(
            $album,
            $request->file('file')
        );

        return response()->json([
            'message' => 'Cover art uploaded successfully',
            'media' => [
                'id' => $media->id,
                'url' => $media->getUrl(),
                'thumb_url' => $media->getUrl('thumb'),
                'medium_url' => $media->getUrl('medium'),
                'large_url' => $media->getUrl('large'),
            ]
        ]);
    }
}
```

---

**Next**: [Spatie Permission Guide](140-spatie-permission-guide.md) | **Previous**: [Aliziodev Laravel Taxonomy Guide](110-aliziodev-laravel-taxonomy-guide.md)

---

*This guide demonstrates comprehensive media management for the Chinook system using Spatie Media Library with Laravel 12, aliziodev/laravel-taxonomy integration, and modern file handling patterns.*
